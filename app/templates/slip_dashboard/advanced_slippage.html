{% extends "base.html" %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/slip_home.css') }}"/>
<link rel="stylesheet" href="{{ url_for('static', filename='css/advanced_slippage.css') }}"/>
<link href='https://fonts.googleapis.com/css?family=Expletus Sans' rel='stylesheet'/>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.1/dist/css/bootstrap.min.css"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"/>
<script src='https://cdn.plot.ly/plotly-latest.min.js'></script>
{% endblock head %}

{% block content %}
<main>
    <div class="container-fluid">
        <!-- Enhanced Page Header -->
        <div class="page-header">
            <h1><i class="fa fa-line-chart"></i> Advanced Slippage Analysis</h1>
            <p>Comprehensive slippage monitoring and trade comparison dashboard</p>
            <div class="refresh-info">
                <div class="status-indicator info">
                    <span class="status-dot"></span>
                    Last updated: <span id="last-update-time">{{ moment().format('DD/MM/YYYY HH:mm:ss') }}</span>
                </div>
            </div>
        </div>

        <!-- Dashboard Summary -->
        <div class="dashboard-summary">
            <div class="summary-metrics">
                <div class="summary-metric positive">
                    <div class="metric-value positive" id="total-slippage">-₹45,230</div>
                    <div class="metric-label">Total Slippage (Favorable)</div>
                    <div class="metric-change positive">↓ 12.5% vs last month</div>
                </div>
                <div class="summary-metric negative">
                    <div class="metric-value negative" id="execution-slippage">₹23,450</div>
                    <div class="metric-label">Execution Slippage</div>
                    <div class="metric-change negative">↑ 8.3% vs last month</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral" id="avg-execution-time">2.34s</div>
                    <div class="metric-label">Avg Execution Time</div>
                    <div class="metric-change positive">↓ 0.2s vs last month</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral" id="total-turnover">₹12.5Cr</div>
                    <div class="metric-label">Total Turnover</div>
                    <div class="metric-change positive">↑ 15.2% vs last month</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral" id="total-trades">1,247</div>
                    <div class="metric-label">Total Trades</div>
                    <div class="metric-change positive">↑ 23 vs last month</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral" id="slip-to-turnover">-18.5 BPS</div>
                    <div class="metric-label">Slip-to-Turnover Ratio</div>
                    <div class="metric-change positive">↓ 2.1 BPS vs last month</div>
                </div>
            </div>
        </div>

        <!-- Dashboard Summary -->
        <div class="dashboard-summary">
            <div class="summary-metrics" id="summary-metrics">
                <div class="summary-metric positive">
                    <div class="metric-value positive" id="total-slippage">Loading...</div>
                    <div class="metric-label">Total Slippage</div>
                    <div class="metric-change positive" id="slippage-change">Calculating...</div>
                </div>
                <div class="summary-metric negative">
                    <div class="metric-value negative" id="execution-slippage">Loading...</div>
                    <div class="metric-label">Execution Slippage</div>
                    <div class="metric-change negative" id="exec-slippage-change">Calculating...</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral" id="avg-execution-time">Loading...</div>
                    <div class="metric-label">Avg Execution Time</div>
                    <div class="metric-change positive" id="exec-time-change">Calculating...</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral" id="total-turnover">Loading...</div>
                    <div class="metric-label">Total Turnover</div>
                    <div class="metric-change positive" id="turnover-change">Calculating...</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral" id="total-trades">Loading...</div>
                    <div class="metric-label">Total Trades</div>
                    <div class="metric-change positive" id="trades-change">Calculating...</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral" id="slip-to-turnover">Loading...</div>
                    <div class="metric-label">Slip-to-Turnover Ratio (BPS)</div>
                    <div class="metric-change positive" id="ratio-change">Calculating...</div>
                </div>
            </div>
        </div>

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="slippage-tab" data-target="slippage-panel" type="button" role="tab">
                    <i class="fa fa-bar-chart"></i> Slippage Monitoring
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="comparison-tab" data-target="comparison-panel" type="button" role="tab">
                    <i class="fa fa-exchange"></i> Trade Vision
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="analytics-tab" data-target="analytics-panel" type="button" role="tab">
                    <i class="fa fa-dashboard"></i> Combined Analytics
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="mainTabContent">
            <!-- Slippage Monitoring Panel -->
            <div class="tab-pane show active" id="slippage-panel" role="tabpanel">
                <!-- Enhanced Filter Panel -->
                <div class="filter-panel">
                    <h5><i class="fa fa-filter"></i> Analysis Filters</h5>

                    <!-- First Row: Date Range, Segment, Strategy/Cluster -->
                    <div class="filter-row">
                        <div class="filter-group date-range">
                            <label class="form-label">Date Range</label>
                            <div class="d-flex gap-2">
                                <input type="date" class="form-control" id="start-date" value="2024-01-01">
                                <input type="date" class="form-control" id="end-date" value="2024-01-31">
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="form-label">Segment</label>
                            <div class="multi-select-wrapper">
                                <div class="multi-select-trigger" data-target="segment-dropdown">
                                    <span class="multi-select-display">Select segments...</span>
                                    <span class="multi-select-count" style="display: none;">0</span>
                                    <i class="fa fa-chevron-down multi-select-arrow"></i>
                                </div>
                                <div class="multi-select-dropdown" id="segment-dropdown">
                                    <div class="multi-select-header">
                                        <input type="text" class="multi-select-search" placeholder="Search segments...">
                                        <div class="multi-select-actions">
                                            <button type="button" class="multi-select-action-btn select-all">Select All</button>
                                            <button type="button" class="multi-select-action-btn clear-all">Clear All</button>
                                        </div>
                                    </div>
                                    <div class="multi-select-options">
                                        <div class="multi-select-option" data-value="OPTIDX">
                                            <input type="checkbox" class="multi-select-checkbox" checked>
                                            <span class="multi-select-label">OPTIDX</span>
                                        </div>
                                        <div class="multi-select-option" data-value="OPTIDX_BSE">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">OPTIDX_BSE</span>
                                        </div>
                                        <div class="multi-select-option" data-value="OPTIDX_US">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">OPTIDX_US</span>
                                        </div>
                                        <div class="multi-select-option" data-value="OPTSTK">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">OPTSTK</span>
                                        </div>
                                        <div class="multi-select-option" data-value="FUTSTK">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">FUTSTK</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="form-label">Strategy/Cluster</label>
                            <div class="multi-select-wrapper">
                                <div class="multi-select-trigger" data-target="strategy-dropdown">
                                    <span class="multi-select-display">Select strategies...</span>
                                    <span class="multi-select-count" style="display: none;">0</span>
                                    <i class="fa fa-chevron-down multi-select-arrow"></i>
                                </div>
                                <div class="multi-select-dropdown" id="strategy-dropdown">
                                    <div class="multi-select-header">
                                        <input type="text" class="multi-select-search" placeholder="Search strategies...">
                                        <div class="multi-select-actions">
                                            <button type="button" class="multi-select-action-btn select-all">Select All</button>
                                            <button type="button" class="multi-select-action-btn clear-all">Clear All</button>
                                        </div>
                                    </div>
                                    <div class="multi-select-options">
                                        <div class="multi-select-option" data-value="strategy_alpha_v1">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">strategy_alpha_v1</span>
                                        </div>
                                        <div class="multi-select-option" data-value="strategy_beta_v2">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">strategy_beta_v2</span>
                                        </div>
                                        <div class="multi-select-option" data-value="strategy_gamma_v1">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">strategy_gamma_v1</span>
                                        </div>
                                        <div class="multi-select-option" data-value="cluster_izmir">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">cluster_izmir</span>
                                        </div>
                                        <div class="multi-select-option" data-value="cluster_kari">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">cluster_kari</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="form-label">Exchange</label>
                            <div class="multi-select-wrapper">
                                <div class="multi-select-trigger" data-target="exchange-dropdown">
                                    <span class="multi-select-display">Select exchanges...</span>
                                    <span class="multi-select-count" style="display: none;">0</span>
                                    <i class="fa fa-chevron-down multi-select-arrow"></i>
                                </div>
                                <div class="multi-select-dropdown" id="exchange-dropdown">
                                    <div class="multi-select-header">
                                        <input type="text" class="multi-select-search" placeholder="Search exchanges...">
                                        <div class="multi-select-actions">
                                            <button type="button" class="multi-select-action-btn select-all">Select All</button>
                                            <button type="button" class="multi-select-action-btn clear-all">Clear All</button>
                                        </div>
                                    </div>
                                    <div class="multi-select-options">
                                        <div class="multi-select-option" data-value="IND">
                                            <input type="checkbox" class="multi-select-checkbox" checked>
                                            <span class="multi-select-label">IND (India Combined)</span>
                                        </div>
                                        <div class="multi-select-option" data-value="NSE">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">NSE (National Stock Exchange)</span>
                                        </div>
                                        <div class="multi-select-option" data-value="BSE">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">BSE (Bombay Stock Exchange)</span>
                                        </div>
                                        <div class="multi-select-option" data-value="US">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">US (United States)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Second Row: Quick Dates, Slave Names, Actions -->
                    <div class="filter-row">
                        <div class="filter-group quick-dates-group">
                            <label class="form-label">Quick Dates</label>
                            <div class="quick-dates">
                                <button class="quick-date-btn" data-days="7">7d</button>
                                <button class="quick-date-btn" data-days="30">30d</button>
                                <button class="quick-date-btn" data-days="90">3m</button>
                                <button class="quick-date-btn" data-days="180">6m</button>
                                <button class="quick-date-btn" data-days="365">1y</button>
                            </div>
                        </div>

                        <div class="filter-group slave-group">
                            <label class="form-label">Slave Names</label>
                            <div class="multi-select-wrapper">
                                <div class="multi-select-trigger" data-target="slave-dropdown">
                                    <span class="multi-select-display">Select slaves...</span>
                                    <span class="multi-select-count" style="display: none;">0</span>
                                    <i class="fa fa-chevron-down multi-select-arrow"></i>
                                </div>
                                <div class="multi-select-dropdown" id="slave-dropdown">
                                    <div class="multi-select-header">
                                        <input type="text" class="multi-select-search" placeholder="Search slaves...">
                                        <div class="multi-select-actions">
                                            <button type="button" class="multi-select-action-btn select-all">Select All</button>
                                            <button type="button" class="multi-select-action-btn clear-all">Clear All</button>
                                        </div>
                                    </div>
                                    <div class="multi-select-options">
                                        <div class="multi-select-option" data-value="slave_001">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_001</span>
                                        </div>
                                        <div class="multi-select-option" data-value="slave_002">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_002</span>
                                        </div>
                                        <div class="multi-select-option" data-value="slave_003">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_003</span>
                                        </div>
                                        <div class="multi-select-option" data-value="slave_hedge_001">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_hedge_001</span>
                                        </div>
                                        <div class="multi-select-option" data-value="slave_hedge_002">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_hedge_002</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="filter-group actions">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary" id="load-slaves-btn">
                                    <i class="fa fa-refresh"></i> Load Slaves
                                </button>
                                <button class="btn btn-primary" id="run-analysis-btn">
                                    <i class="fa fa-play"></i> Run Analysis
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Loading Indicator -->
                <div class="loading-indicator" id="loading-indicator" style="display: none;">
                    <div class="advanced-spinner"></div>
                    <div class="loading-text">Loading analysis data...</div>
                    <div class="loading-subtext">Please wait while we process your request</div>
                </div>

                <!-- Charts Container -->
                <div class="charts-container" id="charts-container">
                    <!-- Overview Chart -->
                    <div class="content-card">
                        <h4>Slippage Trend Overview</h4>
                        <div class="chart-container">
                            <div id="overview-chart" class="chart-placeholder">
                                <div class="chart-placeholder-content">
                                    <i class="fa fa-line-chart fa-3x"></i>
                                    <p>Click "Run Analysis" to load slippage trend chart</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Strategy Comparison Chart -->
                    <div class="content-card">
                        <h4>Strategy Performance Comparison</h4>
                        <div class="chart-container">
                            <div id="strategy-comparison-chart" class="chart-placeholder">
                                <div class="chart-placeholder-content">
                                    <i class="fa fa-bar-chart fa-3x"></i>
                                    <p>Strategy comparison will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timing Analysis Chart -->
                    <div class="content-card">
                        <h4>Execution Timing Distribution</h4>
                        <div class="chart-container">
                            <div id="timing-histogram-chart" class="chart-placeholder">
                                <div class="chart-placeholder-content">
                                    <i class="fa fa-clock-o fa-3x"></i>
                                    <p>Timing analysis will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Daily Slippage Breakdown -->
                    <div class="content-card">
                        <h4>Daily Slippage Breakdown</h4>
                        <div class="chart-container">
                            <div id="daily-slippage-chart" class="chart-placeholder">
                                <div class="chart-placeholder-content">
                                    <i class="fa fa-bar-chart fa-3x"></i>
                                    <p>Daily slippage breakdown will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Intraday Distribution -->
                    <div class="content-card">
                        <h4>Intraday Slippage Pattern</h4>
                        <div class="chart-container">
                            <div id="minute-distribution-chart" class="chart-placeholder">
                                <div class="chart-placeholder-content">
                                    <i class="fa fa-line-chart fa-3x"></i>
                                    <p>Intraday pattern will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Execution Timing by Hour -->
                    <div class="content-card">
                        <h4>Execution Timing Analysis</h4>
                        <div class="chart-container">
                            <div id="execution-timing-chart" class="chart-placeholder">
                                <div class="chart-placeholder-content">
                                    <i class="fa fa-clock-o fa-3x"></i>
                                    <p>Execution timing analysis will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slippage by DTE -->
                    <div class="content-card">
                        <h4>Slippage by Days to Expiry</h4>
                        <div class="chart-container">
                            <div id="slippage-dte-chart" class="chart-placeholder">
                                <div class="chart-placeholder-content">
                                    <i class="fa fa-calendar fa-3x"></i>
                                    <p>DTE analysis will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Hedge vs Normal Analysis -->
                    <div class="content-card">
                        <h4>Hedge vs Normal Trades Analysis</h4>
                        <div class="chart-container">
                            <div id="hedge-normal-chart" class="chart-placeholder">
                                <div class="chart-placeholder-content">
                                    <i class="fa fa-exchange fa-3x"></i>
                                    <p>Hedge vs normal analysis will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Strategy Summary -->
                <div class="data-tables-container" id="data-tables-container" style="display: none;">
                    <div class="content-card">
                        <div class="table-header">
                            <h4>Strategy Summary & Analysis</h4>
                            <div class="table-controls">
                                <div class="table-control-group">
                                    <label class="form-label">Group By</label>
                                    <select class="form-select form-select-sm" id="group-by-select">
                                        <option value="strategy_name">Strategy</option>
                                        <option value="slave_name">Slave</option>
                                        <option value="date">Date</option>
                                        <option value="segment">Segment</option>
                                    </select>
                                </div>
                                <div class="table-control-group">
                                    <label class="form-label">Aggregation</label>
                                    <select class="form-select form-select-sm" id="aggregation-select">
                                        <option value="sum">Sum</option>
                                        <option value="avg">Average</option>
                                        <option value="count">Count</option>
                                        <option value="max">Maximum</option>
                                        <option value="min">Minimum</option>
                                    </select>
                                </div>
                                <div class="table-control-group">
                                    <label class="form-label">Filter Column</label>
                                    <select class="form-select form-select-sm" id="column-filter-select">
                                        <option value="">All Columns</option>
                                        <option value="strategy_name">Strategy</option>
                                        <option value="slave_name">Slave</option>
                                        <option value="total_slippage">Total Slippage</option>
                                        <option value="turnover">Turnover</option>
                                    </select>
                                </div>
                                <div class="table-control-group">
                                    <label class="form-label">Filter Value</label>
                                    <input type="text" class="form-control form-control-sm" id="column-filter-input" placeholder="Enter filter value">
                                </div>
                                <div class="table-control-group">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="btn-group">
                                        <button class="btn btn-sm btn-outline-secondary" id="apply-grouping-btn">Apply Grouping</button>
                                        <button class="btn btn-sm btn-success" id="export-csv-btn">
                                            <i class="fa fa-download"></i> Export CSV
                                        </button>
                                        <button class="btn btn-sm btn-info" id="export-excel-btn">
                                            <i class="fa fa-file-excel-o"></i> Export Excel
                                        </button>
                                        <button class="btn btn-sm btn-warning" id="export-json-btn">
                                            <i class="fa fa-code"></i> Export JSON
                                        </button>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fa fa-download"></i> Bulk Export
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" id="export-all-formats-btn">
                                                    <i class="fa fa-files-o"></i> All Formats (CSV + Excel + JSON)
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="#" id="export-summary-only-btn">
                                                    <i class="fa fa-table"></i> Summary Only (CSV)
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" id="export-charts-btn">
                                                    <i class="fa fa-bar-chart"></i> Charts as Images
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Summary Statistics -->
                        <div class="summary-stats-row" id="summary-stats-row">
                            <div class="summary-stat-item">
                                <span class="stat-label">Total Records:</span>
                                <span class="stat-value" id="total-records">0</span>
                            </div>
                            <div class="summary-stat-item">
                                <span class="stat-label">Filtered Records:</span>
                                <span class="stat-value" id="filtered-records">0</span>
                            </div>
                            <div class="summary-stat-item">
                                <span class="stat-label">Sum Total Slippage:</span>
                                <span class="stat-value" id="sum-total-slippage">₹0</span>
                            </div>
                            <div class="summary-stat-item">
                                <span class="stat-label">Avg Slip-to-Turnover:</span>
                                <span class="stat-value" id="avg-slip-ratio">0.00 BPS</span>
                            </div>
                        </div>

                        <!-- Enhanced Data Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="analysis-data-table">
                                <thead class="table-dark">
                                    <tr id="table-header-row">
                                        <th class="sortable" data-column="strategy_name">
                                            Strategy <i class="fa fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-column="slave_name">
                                            Slave <i class="fa fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-column="date">
                                            Date <i class="fa fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-column="total_slippage">
                                            Total Slippage <i class="fa fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-column="execution_slippage">
                                            Execution Slippage <i class="fa fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-column="turnover">
                                            Turnover <i class="fa fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-column="trade_count">
                                            Trade Count <i class="fa fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-column="slip_to_turnover_bps">
                                            Slip-to-Turnover (BPS) <i class="fa fa-sort"></i>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="analysis-table-body">
                                    <!-- Data will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="table-pagination" id="table-pagination">
                            <div class="pagination-info">
                                Showing <span id="showing-start">0</span> to <span id="showing-end">0</span> of <span id="total-rows">0</span> entries
                            </div>
                            <div class="pagination-controls">
                                <button class="btn btn-sm btn-outline-secondary" id="prev-page-btn" disabled>
                                    <i class="fa fa-chevron-left"></i> Previous
                                </button>
                                <select class="form-select form-select-sm" id="page-size-select">
                                    <option value="25">25 per page</option>
                                    <option value="50" selected>50 per page</option>
                                    <option value="100">100 per page</option>
                                    <option value="all">Show All</option>
                                </select>
                                <button class="btn btn-sm btn-outline-secondary" id="next-page-btn" disabled>
                                    Next <i class="fa fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trade Vision Panel -->
            <div class="tab-pane fade" id="comparison-panel" role="tabpanel">
                <div class="filter-panel">
                    <h5><i class="fa fa-filter"></i> Comparison Filters</h5>
                    <div class="filter-row">
                        <div class="filter-group date-range">
                            <label class="form-label">Date Range</label>
                            <div class="d-flex gap-2">
                                <input type="date" class="form-control" id="comparison-start-date" value="2024-01-01">
                                <input type="date" class="form-control" id="comparison-end-date" value="2024-01-31">
                            </div>
                        </div>
                        <div class="filter-group">
                            <label class="form-label">Slave/Strategy</label>
                            <input type="text" class="form-control" id="comparison-slave-input" placeholder="Enter slave/strategy name">
                        </div>
                        <div class="filter-group">
                            <label class="form-label">Segment</label>
                            <select class="form-select" id="comparison-segment-select">
                                <option value="OPTIDX" selected>OPTIDX</option>
                                <option value="OPTIDX_BSE">OPTIDX_BSE</option>
                                <option value="OPTIDX_US">OPTIDX_US</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="form-label">Exchange</label>
                            <select class="form-select" id="comparison-exchange-select">
                                <option value="IND" selected>IND</option>
                                <option value="NSE">NSE</option>
                                <option value="BSE">BSE</option>
                                <option value="US">US</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-primary w-100" id="run-comparison-btn">Run Comparison</button>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Comparison Results -->
                <div class="comparison-results" id="comparison-results" style="display: none;">
                    <!-- Comparison Summary Card -->
                    <div class="content-card">
                        <h4><i class="fa fa-exchange"></i> Strategy vs Cluster vs Backtest Comparison</h4>

                        <!-- Key Performance Indicators -->
                        <div class="comparison-kpis">
                            <div class="kpi-section">
                                <h6>Strategy vs Cluster</h6>
                                <div class="metrics-grid" id="strategy-cluster-metrics">
                                    <!-- Metrics populated by JavaScript -->
                                </div>
                            </div>
                            <div class="kpi-section">
                                <h6>Strategy vs Backtest</h6>
                                <div class="metrics-grid" id="strategy-backtest-metrics">
                                    <!-- Metrics populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Comparison Charts -->
                    <div class="content-card">
                        <h4><i class="fa fa-bar-chart"></i> Performance Comparison Charts</h4>

                        <!-- Chart Navigation Tabs -->
                        <ul class="nav nav-pills nav-fill mb-3" id="comparison-chart-tabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="exit-count-tab" data-target="exit-count-panel" type="button" role="tab">
                                    Exit Count Comparison
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pnl-comparison-tab" data-target="pnl-comparison-panel" type="button" role="tab">
                                    PnL Comparison
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="holding-time-tab" data-target="holding-time-panel" type="button" role="tab">
                                    Holding Time Distribution
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="performance-summary-tab" data-target="performance-summary-panel" type="button" role="tab">
                                    Performance Summary
                                </button>
                            </li>
                        </ul>

                        <!-- Chart Tab Content -->
                        <div class="tab-content" id="comparison-chart-content">
                            <!-- Exit Count Panel -->
                            <div class="tab-pane show active" id="exit-count-panel" role="tabpanel">
                                <div class="chart-container">
                                    <div class="chart-header">
                                        <div class="chart-title">Daily Exit Count Comparison</div>
                                        <div class="chart-controls">
                                            <button class="btn btn-sm btn-outline-secondary" id="toggle-exit-count-view">
                                                <i class="fa fa-refresh"></i> Toggle View
                                            </button>
                                        </div>
                                    </div>
                                    <div id="exit-count-chart" class="chart-placeholder">
                                        <div class="chart-placeholder-content">
                                            <i class="fa fa-line-chart fa-2x"></i>
                                            <p>Exit count comparison will appear here</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- PnL Comparison Panel -->
                            <div class="tab-pane fade" id="pnl-comparison-panel" role="tabpanel">
                                <div class="chart-container">
                                    <div class="chart-header">
                                        <div class="chart-title">Cumulative PnL Comparison</div>
                                        <div class="chart-controls">
                                            <button class="btn btn-sm btn-outline-secondary" id="toggle-pnl-view">
                                                <i class="fa fa-line-chart"></i> Line View
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" id="reset-pnl-zoom">
                                                <i class="fa fa-search-minus"></i> Reset Zoom
                                            </button>
                                        </div>
                                    </div>
                                    <div id="pnl-comparison-chart" class="chart-placeholder">
                                        <div class="chart-placeholder-content">
                                            <i class="fa fa-area-chart fa-2x"></i>
                                            <p>PnL comparison will appear here</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Holding Time Panel -->
                            <div class="tab-pane fade" id="holding-time-panel" role="tabpanel">
                                <div class="chart-container">
                                    <div class="chart-header">
                                        <div class="chart-title">Holding Time Distribution</div>
                                        <div class="chart-controls">
                                            <select class="form-select form-select-sm" id="holding-time-bins">
                                                <option value="20">20 bins</option>
                                                <option value="30" selected>30 bins</option>
                                                <option value="50">50 bins</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div id="holding-time-chart" class="chart-placeholder">
                                        <div class="chart-placeholder-content">
                                            <i class="fa fa-bar-chart fa-2x"></i>
                                            <p>Holding time distribution will appear here</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Performance Summary Panel -->
                            <div class="tab-pane fade" id="performance-summary-panel" role="tabpanel">
                                <div class="performance-summary-container">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="table-responsive">
                                                <table class="table table-striped table-hover" id="performance-summary-table">
                                                    <thead class="table-dark">
                                                        <tr>
                                                            <th>Metric</th>
                                                            <th>Strategy</th>
                                                            <th>Cluster</th>
                                                            <th>Backtest</th>
                                                            <th>Strategy vs Cluster</th>
                                                            <th>Strategy vs Backtest</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="performance-summary-body">
                                                        <!-- Data populated by JavaScript -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="performance-insights">
                                                <h6><i class="fa fa-lightbulb-o"></i> Key Insights</h6>
                                                <div id="performance-insights-content">
                                                    <!-- Insights populated by JavaScript -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Analysis -->
                    <div class="content-card">
                        <h4><i class="fa fa-table"></i> Detailed Trade Analysis</h4>

                        <div class="analysis-controls">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">Analysis Type</label>
                                    <select class="form-select" id="analysis-type-select">
                                        <option value="daily">Daily Analysis</option>
                                        <option value="weekly">Weekly Analysis</option>
                                        <option value="monthly">Monthly Analysis</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Metric Focus</label>
                                    <select class="form-select" id="metric-focus-select">
                                        <option value="exit_count">Exit Count</option>
                                        <option value="pnl">PnL</option>
                                        <option value="holding_time">Holding Time</option>
                                        <option value="all">All Metrics</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Short Duration Threshold</label>
                                    <input type="number" class="form-control" id="short-duration-threshold" value="5" min="1" max="60">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <button class="btn btn-primary w-100" id="refresh-analysis-btn">
                                        <i class="fa fa-refresh"></i> Refresh Analysis
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="detailed-analysis-results" id="detailed-analysis-results">
                            <!-- Results populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Combined Analytics Panel -->
            <div class="tab-pane fade" id="analytics-panel" role="tabpanel">
                <div class="content-card">
                    <h4>Combined Analytics Dashboard</h4>
                    <p class="text-muted">Integrated view combining slippage analysis with trade comparison metrics</p>
                    
                    <!-- Coming Soon Placeholder -->
                    <div class="text-center py-5">
                        <i class="fa fa-cogs fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Advanced Analytics Coming Soon</h5>
                        <p class="text-muted">This section will provide integrated analysis combining both slippage monitoring and trade comparison features.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script src="{{ url_for('static', filename='scripts/advanced_slippage.js') }}"></script>
{% endblock content %}
